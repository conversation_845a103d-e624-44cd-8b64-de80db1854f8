import 'dart:async';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/model_config.dart';

/// Gemini API服务类
/// 基于obsidian-smart-composer-1.2.1的实现方式
class GeminiService {
  late GenerativeModel _model;
  late String _apiKey;
  late ModelConfig _config;

  GeminiService({required ModelConfig config}) {
    _config = config;
    _apiKey = config.apiKey;

    if (_apiKey.isEmpty) {
      throw Exception('Gemini API Key未配置');
    }

    // 直接创建生成模型
    _model = GenerativeModel(
      model: config.model,
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: config.maxTokens,
        temperature: config.temperature,
        topP: config.topP,
      ),
    );
  }

  /// 生成文本响应（非流式）
  Future<String> generateText({
    required String prompt,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
  }) async {
    try {
      // 构建内容
      final content = _buildContent(prompt, systemPrompt);

      // 如果有自定义参数，创建新的模型实例
      GenerativeModel model = _model;
      if (temperature != null || maxTokens != null) {
        model = GenerativeModel(
          model: _config.model,
          apiKey: _apiKey,
          generationConfig: GenerationConfig(
            maxOutputTokens: maxTokens ?? _config.maxTokens,
            temperature: temperature ?? _config.temperature,
            topP: _config.topP,
          ),
          systemInstruction: systemPrompt != null ? Content.text(systemPrompt) : null,
        );
      }

      final response = await model.generateContent([content]);

      // 检查响应是否有效
      if (response.text == null) {
        throw Exception('Gemini API返回null响应');
      }

      if (response.text!.isEmpty) {
        throw Exception('Gemini API返回空响应');
      }

      return response.text!;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 生成文本响应（流式）
  Stream<String> generateTextStream({
    required String prompt,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
  }) async* {
    try {
      // 构建内容
      final content = _buildContent(prompt, systemPrompt);

      // 如果有自定义参数，创建新的模型实例
      GenerativeModel model = _model;
      if (temperature != null || maxTokens != null) {
        model = GenerativeModel(
          model: _config.model,
          apiKey: _apiKey,
          generationConfig: GenerationConfig(
            maxOutputTokens: maxTokens ?? _config.maxTokens,
            temperature: temperature ?? _config.temperature,
            topP: _config.topP,
          ),
          systemInstruction: systemPrompt != null ? Content.text(systemPrompt) : null,
        );
      }

      final stream = model.generateContentStream([content]);

      await for (final chunk in stream) {
        if (chunk.text != null && chunk.text!.isNotEmpty) {
          yield chunk.text!;
        }
      }
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 获取嵌入向量
  Future<List<double>> getEmbedding({
    required String text,
    String? model,
  }) async {
    try {
      final embeddingModel = GenerativeModel(
        model: model ?? 'text-embedding-004',
        apiKey: _apiKey,
      );

      final response = await embeddingModel.embedContent(Content.text(text));
      return response.embedding.values;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 构建内容对象
  Content _buildContent(String prompt, String? systemPrompt) {
    String fullPrompt = prompt;
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      fullPrompt = '$systemPrompt\n\n$prompt';
    }
    return Content.text(fullPrompt);
  }

  /// 处理错误
  Exception _handleError(dynamic error) {
    final errorMessage = error.toString();
    
    // API密钥无效
    if (errorMessage.contains('API_KEY_INVALID') || 
        errorMessage.contains('API key not valid')) {
      return Exception('Gemini API密钥无效，请在设置中更新API密钥');
    }
    
    // 速率限制
    if (errorMessage.contains('429') || errorMessage.contains('rate limit')) {
      return Exception('Gemini API速率限制，请稍后重试');
    }
    
    // 网络连接问题
    if (errorMessage.contains('SocketException') || 
        errorMessage.contains('TimeoutException')) {
      return Exception('无法连接到Gemini API，请检查网络连接');
    }
    
    // 其他错误
    return Exception('Gemini API调用失败: $errorMessage');
  }

  /// 验证API密钥
  static Future<bool> validateApiKey(String apiKey) async {
    try {
      final model = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          maxOutputTokens: 10,
          temperature: 0.1,
        ),
      );

      final response = await model.generateContent([Content.text('Hello')]);
      return response.text != null && response.text!.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 获取可用模型列表
  static List<String> getAvailableModels() {
    return [
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      'gemini-2.0-flash-exp',
    ];
  }
}
